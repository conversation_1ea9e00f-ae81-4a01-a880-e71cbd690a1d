import { versions } from './versions';

export const environment = {
  versions,
  production: false,
  hostUrl: '',
  apiUrl: 'https://qa.baseprojectjava.thesunflowerlab.com/api',
  frontendUrl: 'https://qa.octopusmusicschool.com/',
  persistUserSession: true,
  enableLaunchPage: false,
  sentryKey: '', // sentry key not required for local environments
  firebase: {
    apiKey: '',
    authDomain: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
    measurementId: ''
  },
  swUpdate: false,
  swUpdateFooter: false,
  encryptionService: {
    enable: false,
    secretKey: 'BfBRoYxsRKGnx3DlPgnsmei7qVHTD62o',
    secretIV: 'cGsgbxxqbcYN6HZG'
  },
  compressAfterFileSize: 2
};
