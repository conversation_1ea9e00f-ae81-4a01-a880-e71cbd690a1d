import { versions } from './versions';

export const environment = {
  versions,
  production: true,
  hostUrl: '',
  apiUrl: 'https://stage-api.octopusmusicschool.com/api',
  frontendUrl: 'https://squid.octopusmusicschool.com/',
  signalRUrl: 'https://stage-api.octopusmusicschool.com/signalr-octopus-chat',
  persistUserSession: true,
  enableLaunchPage: true,
  sentryKey: '', // sentry key not required for local environments,
  paymentTokenizationKey: 'Yuk8nS-8zkE3n-6Epe2E-265ft2',
  firebase: {
    apiKey: "AIzaSyCvPrKMb_Zm8E0uG7hysCd-XMwUSwfflY0",
    authDomain: "octopus-stage-58452.firebaseapp.com",
    projectId: "octopus-stage-58452",
    storageBucket: "octopus-stage-58452.firebasestorage.app",
    messagingSenderId: "************",
    appId: "1:************:web:16750bfebb0fc8742803bd"
  },
  swUpdate: true,
  swUpdateFooter: true,
  encryptionService: {
    enable: false,
    secretKey: 'BfBRoYxsRKGnx3DlPgnsmei7qVHTD62o',
    secretIV: 'cGsgbxxqbcYN6HZG'
  },
  compressAfterFileSize: 2
};
