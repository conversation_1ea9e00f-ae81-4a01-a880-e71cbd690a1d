import { Injectable } from '@angular/core';

declare let gtag: Function;
declare let dataLayer: any[];

@Injectable({
  providedIn: 'root'
})
export class AnalyticsService {

  constructor() {
    // Initialize dataLayer if not exists
    if (typeof dataLayer === 'undefined') {
      (window as any).dataLayer = [];
    }
  }

  /**
   * Send custom event to Google Analytics via GTM
   */
  trackEvent(eventName: string, parameters?: any): void {
    if (typeof dataLayer !== 'undefined') {
      dataLayer.push({
        event: eventName,
        ...parameters
      });
      console.log('Analytics Event Tracked:', eventName, parameters);
    }
  }

  /**
   * Track page views
   */
  trackPageView(pagePath: string, pageTitle?: string): void {
    this.trackEvent('page_view', {
      page_path: pagePath,
      page_title: pageTitle
    });
  }

  /**
   * Track user login
   */
  trackLogin(method: string = 'email'): void {
    this.trackEvent('login', {
      method: method
    });
  }

  /**
   * Track user registration
   */
  trackSignUp(method: string = 'email'): void {
    this.trackEvent('sign_up', {
      method: method
    });
  }

  /**
   * Track button clicks
   */
  trackButtonClick(buttonName: string, location?: string): void {
    this.trackEvent('button_click', {
      button_name: buttonName,
      location: location
    });
  }

  /**
   * Track form submissions
   */
  trackFormSubmit(formName: string, success: boolean = true): void {
    this.trackEvent('form_submit', {
      form_name: formName,
      success: success
    });
  }

  /**
   * Track student actions
   */
  trackStudentAction(action: string, studentId?: number): void {
    this.trackEvent('student_action', {
      action: action,
      student_id: studentId
    });
  }

  /**
   * Track attendance marking
   */
  trackAttendance(isPresent: boolean, studentId: number, classType?: string): void {
    this.trackEvent('mark_attendance', {
      attendance_status: isPresent ? 'present' : 'absent',
      student_id: studentId,
      class_type: classType
    });
  }

  /**
   * Track payment events
   */
  trackPayment(amount: number, currency: string = 'USD', paymentMethod?: string): void {
    this.trackEvent('purchase', {
      transaction_id: Date.now().toString(),
      value: amount,
      currency: currency,
      payment_method: paymentMethod
    });
  }

  /**
   * Track navigation events
   */
  trackNavigation(fromPage: string, toPage: string): void {
    this.trackEvent('navigation', {
      from_page: fromPage,
      to_page: toPage
    });
  }

  /**
   * Track search events
   */
  trackSearch(searchTerm: string, category?: string): void {
    this.trackEvent('search', {
      search_term: searchTerm,
      category: category
    });
  }

  /**
   * Track errors
   */
  trackError(errorMessage: string, errorLocation?: string): void {
    this.trackEvent('exception', {
      description: errorMessage,
      location: errorLocation,
      fatal: false
    });
  }
}
