import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { CommonModule, Location } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonUtils } from 'src/app/shared/utils';
import { SchedulerService } from '../scheduler-wrapper/pages/schedule/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import {
  ClassTypes,
  LessonTypes,
  ScheduleDetailsView,
  StudentDetail
} from '../scheduler-wrapper/pages/schedule/models';
import { AuthService } from 'src/app/auth/services';
import { AttendanceService } from 'src/app/pages/attendance/services';
import { AnalyticsService, AppToasterService, NavigationService } from 'src/app/shared/services';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Instruments } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { LocalDatePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [SharedModule, CommonModule, MatIconModule, MatButtonModule, MatTooltipModule],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-scheduler-details',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './scheduler-details.component.html',
  styleUrl: './scheduler-details.component.scss'
})
export class SchedulerDetailsComponent extends BaseComponent implements OnInit {
  viewScheduleId!: string;
  viewScheduleContent!: ScheduleDetailsView | undefined;
  LESSON_TYPE = LessonTypes;
  classTypes = ClassTypes;

  constructor(
    private readonly activatedRouter: ActivatedRoute,
    protected readonly schedulerService: SchedulerService,
    private readonly locationService: Location,
    private readonly authService: AuthService,
    private readonly attendanceService: AttendanceService,
    private readonly toasterService: AppToasterService,
    private readonly analyticsService: AnalyticsService,
    private readonly navigationService: NavigationService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit() {
    this.activatedRouter.params.subscribe((params: any) => {
      this.viewScheduleId = params.id;
      this.getCurrentUser();
      this.getScheduleDetails(params.id);
    });
  }

  getInstrumentNames(array: Instruments[]) {
      return array
        .slice(2)
        .map(item => item.instrumentName + '(' + item.instrumentGrade + ')')
        .join(', ');
    }

  getInitials(name: string | undefined): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.currentUser = res;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getScheduleDetails(id: number): void {
    this.showPageLoader = true;
    this.schedulerService
      .get<CBGetResponse<ScheduleDetailsView>>(
        `${API_URL.scheduleLessonDetails.getScheduleLessonDetailForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetailsView>) => {
          this.viewScheduleContent = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  openStudentDetails(dependentId: number): void {
    this.navigationService.navigateToStudentDetail(dependentId);
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  isInvalidAttendance(): boolean {
    if (this.viewScheduleContent?.isCancelSchedule) {
      this.toasterService.error(this.constants.errorMessages.invalidAttendance.replace('{item}', 'canceled lesson'));
      return true;
    }
    if (this.viewScheduleContent?.isDraftSchedule) {
      this.toasterService.error(this.constants.errorMessages.invalidAttendance.replace('{item}', 'draft lesson'));
      return true;
    }
    if (this.isFutureEvent(this.viewScheduleContent?.scheduleDate)) {
      this.toasterService.error(this.constants.errorMessages.invalidAttendance.replace('{item}', 'future dates'));
      return true;
    }
    return false;
  }

  markAttendance(isPresent: boolean, studentDetail: StudentDetail): void {
    if (this.isInvalidAttendance()) {
      return;
    }
    this.attendanceService
      .add(
        {
          studentId: studentDetail.studentId,
          scheduleLessonDetailId: this.viewScheduleContent?.id,
          classType: this.viewScheduleContent?.classType,
          isPresent: isPresent
        },
        API_URL.crud.createOrUpdate
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // Track attendance marking in analytics
          this.analyticsService.trackAttendance(
            isPresent,
            studentDetail.studentId,
            this.viewScheduleContent?.classType?.toString()
          );

          this.getScheduleDetails(this.viewScheduleContent?.id!);
          this.toasterService.success(
            this.constants.successMessages.markedSuccessfully.replace('{item}', 'Attendance')
          );
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  goBack(): void {
    this.locationService.back();
  }

  isFutureEvent(scheduleDate?: string): boolean {
    const currentDate = new Date();
    const formattedDate = new Date(scheduleDate!);
    return formattedDate > currentDate;
  }
}
