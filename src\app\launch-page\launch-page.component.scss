@import 'src/assets/scss/variables';
@import 'src/assets/scss/theme/_mixins.scss';

.launch-page-container {
  min-height: 100vh;
  display: flex;

  .image-wrapper {
    background-color: $primary-color;
    background-repeat: no-repeat;
    height: 100vh;
    width: 500px;

    .octopus-logo {
      display: flex;
      justify-self: center;
      margin-top: 60px;
    }
  }

  .content-wrapper {
    @include flex-content-center;
    text-align: center;
    padding: 80px 60px;
    width: calc(100% - 500px);

    .main-content {
      .main-title {
        font-size: 40px;
        font-weight: 700;
        color: $un-auth-title-color;
        margin-bottom: 30px;
        line-height: 1.2;
      }

      .subtitle {
        font-size: 17px;
        color: $gray-text;
        margin-bottom: 50px;
        line-height: 1.6;
        max-width: 600px;
        font-weight: 600;
        margin-left: auto;
        margin-right: auto;
      }

      .launch-message {
        font-size: 18px;
        color: $gray-text;
        margin-bottom: 30px;
        font-weight: 700;
      }

      .countdown-section {
        margin-bottom: 50px;

        .countdown-labels {
          @include flex-content-center;
          gap: 60px;
          margin-bottom: 20px;

          .countdown-label {
            font-size: 16px;
            color: $gray-text-light;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 2px;
          }
        }

        .countdown-timer {
          @include flex-content-center;
          gap: 25px;
          font-size: 60px;
          font-weight: 700;
          color: $black-color;

          .countdown-number {
            display: inline-block;
            min-width: 80px;
          }

          .countdown-separator {
            color: $gray-text-light;
          }
        }
      }

      .action-buttons {
        @include flex-content-center;
        gap: 25px;
        flex-wrap: wrap;

        button {
          min-height: 40px !important;
          padding: 8px 20px !important;
          font-size: 17px !important;
          font-weight: 500 !important;
          border-radius: 8px !important;

          &:disabled {
            background-color: $disable-background-color !important;
            color: $disable-color !important;
          }
        }

        .enroll-btn {
          background-color: $primary-color !important;
          color: $white-color !important;
        }

        .reset-password-btn {
          background-color: $gray-bg-light !important;
          color: $white-color !important;
        }
      }
    }
  }
}

@media (max-width: 1050px) {
  .image-wrapper {
    display: none;
  }
  .launch-page-container {
    .content-wrapper {
      width: 100%;
      padding: 30px 20px;

      .logo-section {
        .octopus-title {
          font-size: 28px;
          letter-spacing: 4px;
        }
      }

      .main-content {
        .main-title {
          font-size: 28px;
        }

        .subtitle {
          font-size: 16px;
        }

        .countdown-section {
          .countdown-labels {
            gap: 30px;

            .countdown-label {
              font-size: 12px;
            }
          }

          .countdown-timer {
            font-size: 42px;
            gap: 10px;

            .countdown-number {
              min-width: 50px;
            }
          }
        }

        .launch-message {
          font-size: 14px;
          color: $gray-text;
          margin-bottom: 30px;
          font-weight: 700;
        }
      }
    }
  }
}
