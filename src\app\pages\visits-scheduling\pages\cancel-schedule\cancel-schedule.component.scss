@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.lesson-cancel-wrapper {
  @include flex-content-center;
  text-align: center;
  height: calc(100vh - 140px);
  padding: 0px 70px;

  .success-title {
    margin: 20px 0px;
    font-size: 32px;
    font-weight: 700;
  }

  .success-message {
    color: $gray-text;
    font-size: 20px;

    span {
      color: $black-color;
    }
  }
}

.schedule-info-wrapper {
  background: $header-schedule-bg-color;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;

  .schedule-header {
    @include flex-content-space-between;
    font-size: 16px;
    line-height: 1;
    .name {
      font-weight: 700;
    }

    .class-info-wrapper {
      @include flex-content-align-center;
    }
  }

  .schedule-content-wrapper {
    @include flex-content-align-center;
    font-size: 14px;
    line-height: 1;
    margin-top: 12px;

    .schedule-content {
      flex-wrap: wrap;
      @include flex-content-align-center;

      .img-icon {
        filter: $primary-color-filter;
      }

      .instructor-img,
      .img-icon {
        height: 14px;
        margin-right: 5px;
      }

      .info-content {
        font-weight: 600;
        display: flex;

        .info-label {
          font-weight: 700;
        }

        .primary-text {
          color: $primary-color;
        }

        .display-content{
          display: flex;
        }
      }
    }
  }
}

::ng-deep {
  .mat-drawer-content {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
}
